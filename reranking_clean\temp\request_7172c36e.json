{"amount_in": "5521104220", "token_in": "0xdac17f958d2ee523a2206206994597c13d831ec7", "token_in_decimals": 6, "token_out": "0x6b175474e89094c44da98b954eedeac495271d0f", "token_out_decimals": 18, "liquidity": 3057769414167515267314, "sqrt_price": "79212444899498758091138", "fee": 100, "tick": -276328, "ticks": [{"tick": -887272, "liquidity_net": "101041786155430", "sqrt_price": "0"}, {"tick": -276840, "liquidity_net": "809353136220737", "sqrt_price": "0"}, {"tick": -276370, "liquidity_net": "24425838310096547835", "sqrt_price": "0"}, {"tick": -276344, "liquidity_net": "99170541131100", "sqrt_price": "0"}, {"tick": -276343, "liquidity_net": "152376927527190466261", "sqrt_price": "0"}, {"tick": -276342, "liquidity_net": "49136238908544129", "sqrt_price": "0"}, {"tick": -276340, "liquidity_net": "398633310109411", "sqrt_price": "0"}, {"tick": -276338, "liquidity_net": "1138421928453627675899", "sqrt_price": "0"}, {"tick": -276337, "liquidity_net": "886764875770070367328", "sqrt_price": "0"}, {"tick": -276336, "liquidity_net": "299483369273055342", "sqrt_price": "0"}, {"tick": -276335, "liquidity_net": "161292395966967116222", "sqrt_price": "0"}, {"tick": -276334, "liquidity_net": "1303440094285280957", "sqrt_price": "0"}, {"tick": -276333, "liquidity_net": "15158164321571701332", "sqrt_price": "0"}, {"tick": -276332, "liquidity_net": "6580653158374074", "sqrt_price": "0"}, {"tick": -276331, "liquidity_net": "675423121300314702290", "sqrt_price": "0"}, {"tick": -276330, "liquidity_net": "43971541471533855", "sqrt_price": "0"}, {"tick": -276329, "liquidity_net": "1999368859750471801", "sqrt_price": "0"}, {"tick": -276328, "liquidity_net": "202773562055813311", "sqrt_price": "0"}, {"tick": -276327, "liquidity_net": "320466571662817579685", "sqrt_price": "0"}, {"tick": -276326, "liquidity_net": "269300482105873772", "sqrt_price": "0"}, {"tick": -276325, "liquidity_net": "4232867027613212083684", "sqrt_price": "0"}, {"tick": -276324, "liquidity_net": "-92817821576279614976", "sqrt_price": "0"}, {"tick": -276323, "liquidity_net": "94874117696132661185", "sqrt_price": "0"}, {"tick": -276322, "liquidity_net": "12919576657070817", "sqrt_price": "0"}, {"tick": -276321, "liquidity_net": "-103275659657917579451", "sqrt_price": "0"}, {"tick": -276320, "liquidity_net": "-3903038846962011326809", "sqrt_price": "0"}, {"tick": -276319, "liquidity_net": "-674258710660210850512", "sqrt_price": "0"}, {"tick": -276318, "liquidity_net": "-50428771711296252346", "sqrt_price": "0"}, {"tick": -276317, "liquidity_net": "-891268809464582902855", "sqrt_price": "0"}, {"tick": -276316, "liquidity_net": "-206331876423177166273", "sqrt_price": "0"}, {"tick": -276315, "liquidity_net": "-674522094196862770506", "sqrt_price": "0"}, {"tick": -276314, "liquidity_net": "-1075482999863542419467", "sqrt_price": "0"}, {"tick": -276313, "liquidity_net": "-433085475422585544", "sqrt_price": "0"}, {"tick": -276312, "liquidity_net": "-2141752835351486", "sqrt_price": "0"}, {"tick": -276311, "liquidity_net": "-10824695179444004", "sqrt_price": "0"}, {"tick": -276310, "liquidity_net": "-9610320646610390698", "sqrt_price": "0"}, {"tick": -276308, "liquidity_net": "-10424815768265963", "sqrt_price": "0"}, {"tick": -276306, "liquidity_net": "-192836083098624304", "sqrt_price": "0"}, {"tick": -276305, "liquidity_net": "-1949943270408685", "sqrt_price": "0"}, {"tick": -276304, "liquidity_net": "-99170541131100", "sqrt_price": "0"}, {"tick": -276302, "liquidity_net": "-49136238908544129", "sqrt_price": "0"}, {"tick": -276301, "liquidity_net": "-25214605322404163", "sqrt_price": "0"}, {"tick": -276299, "liquidity_net": "-67858711532858253", "sqrt_price": "0"}, {"tick": -276297, "liquidity_net": "-24425838310096547835", "sqrt_price": "0"}, {"tick": -276296, "liquidity_net": "1", "sqrt_price": "0"}, {"tick": -276295, "liquidity_net": "-1", "sqrt_price": "0"}, {"tick": -276290, "liquidity_net": "-3119839050720931", "sqrt_price": "0"}, {"tick": -275839, "liquidity_net": "-809353136220737", "sqrt_price": "0"}, {"tick": 887272, "liquidity_net": "-101041786155430", "sqrt_price": "0"}]}