# Crypto Arbitrage Max Flow - Reranking

## Overview

## Main Orchestrator

### `run_experiment.py` - Master Control Script

This is the **main entry point** that orchestrates the entire workflow. It automates the complete experimental pipeline from data loading to final analysis.

**Key Features:**

-   Manages the complete 6-stage workflow
-   <PERSON><PERSON> directory setup and cleanup
-   Provides comprehensive error handling and logging
-   Supports various configuration options

**Usage:**

```bash
python run_experiment.py --block_number <BLOCK_NUMBER> [--max_length N] [--threshold T] [--split_ticks]
```

## Core Components

### Data Processing Files

#### `graph.py` - Market Graph Generator

**Purpose:** Converts raw blockchain pool data into a structured market graph

-   **Input:** Pool data from `data/prices/block-<BLOCK_NUMBER>/`
-   **Output:**
    -   `data/graph_data/G_<BLOCK_NUMBER>.json` (main graph file)
    -   `graphs/<BLOCK_NUMBER>_combined_token_graph.txt` (edge list format)
    -   `graphs/<BLOCK_NUMBER>_combined_node_map.txt` (node mappings)
    -   `graphs/<BLOCK_NUMBER>_pool_capacities.json` (liquidity data)
    -   `graphs/<BLOCK_NUMBER>_pool_weights.json` (price data)

**Key Functions:**

-   Processes Uniswap V2 and V3 pools
-   Handles tick-level data for V3 pools
-   Applies capacity thresholds for filtering
-   Supports tick splitting for large price ranges

#### `process_v3_data.py` - V3 Tick Processor

**Purpose:** Preprocesses Uniswap V3 tick data for improved granularity

-   **Input:** Raw V3 pool data
-   **Output:** Enhanced tick data with split ranges
-   **When Used:** Only when `--split_ticks` flag is enabled

### Path Finding and Allocation

#### `path_allocation.py` - Core Allocation Algorithm

**Purpose:** Implements the sophisticated path allocation algorithm with reranking

-   **Input:**
    -   Graph data from `graph.py`
    -   Path enumeration results from `results/path/`
    -   Trading pairs and amounts from command line
-   **Output:** `results/path_allocation_json/allocation_*.json`

**Key Features:**

-   Multi-round reranking algorithm
-   Capacity-aware path selection
-   Detailed logging and statistics
-   Support for multiple path lengths

#### `dfs.cpp` / `dfs.exe` - Path Enumeration

**Purpose:** High-performance C++ implementation for finding all possible paths

-   **Input:** Graph file, source/target nodes, max path length
-   **Output:** `results/path/all_paths_<source>_<target>_<length>.txt`
-   **Compilation:** `g++ -O3 dfs.cpp -o dfs`

#### `remove_cycles.py` - Cycle Detection and Removal

**Purpose:** Removes cycles from allocation plans to ensure valid execution

-   **Input:** JSON files in `results/path_allocation_json/`
-   **Output:** Updates files in place, logs to `results/log/`
-   **Algorithm:** Removes minimum-capacity edges to break cycles

### Simulation and Testing

#### `simulator.py` - Trade Execution Simulator

**Purpose:** Simulates trade execution using a local blockchain node API

-   **Input:** Allocation plans from `results/path_allocation_json/`
-   **Output:** `results/simulation_json/` (execution results)
-   **API:** Connects to `http://127.0.0.1:8080` for simulation
-   **Features:**
    -   Batch processing mode
    -   Detailed execution logging
    -   Error handling and retry logic

## Data Structure

### Input Data Locations

#### `data/benchmark/<BLOCK_NUMBER>_benchmark/`

**Contains:** Benchmark trading data for comparison

-   **Format:** JSON files with structure:
    ```json
    {
      "blockNumber": 23018373,
      "fromAmount": "1000000000000",
      "from": "0x...",
      "toAmount": "872197982382429502258461",
      "to": "0x...",
      "route": { "fills": [...] }
    }
    ```
-   **Naming:** `<block>_<sell>-<buy>_<amount>.json`

#### `data/node_name.txt`

**Contains:** Token symbol to address mappings

-   **Format:** `SYMBOL 0xaddress...`
-   **Purpose:** Maps human-readable symbols to contract addresses

#### `data/prices/block-<BLOCK_NUMBER>/`

**Contains:** Raw pool state data for the specific block

-   **Source:** On-chain liquidity pool snapshots
-   **Format:** JSON arrays of pool state objects

#### `data/token-decimals.csv`

**Contains:** Token decimal information for proper amount calculations

### Output Data Locations

#### `results/path_allocation_json/`

**Contains:** Generated allocation plans

-   **Format:** JSON files with path allocations and amounts
-   **Naming:** `allocation_all_paths_<pair>_<length>_<amount>.json`

#### `results/simulation_json/`

**Contains:** Simulation execution results

-   **Format:** JSON files with execution outcomes
-   **Purpose:** Compare algorithm performance vs benchmarks

#### `results/path/`

**Contains:** Enumerated path files

-   **Format:** Text files listing all possible paths
-   **Naming:** `all_paths_<source>_<target>_<length>.txt`

#### `results/log/`

**Contains:** Detailed execution logs

-   **Types:** Allocation logs, cycle removal logs, simulation debug logs
-   **Purpose:** Debugging and performance analysis

#### `results/analysis/`

**Contains:** Final analysis and comparison reports

-   **Format:** CSV files comparing algorithm vs benchmark performance

## Workflow Stages

### Stage 1: Load Test Cases

-   Scans `data/benchmark/<BLOCK_NUMBER>_benchmark/` for JSON files
-   Extracts trading pairs and amounts
-   Validates against available token mappings

### Stage 2: Generate Market Graph

-   Processes pool data into graph format
-   Applies filtering and thresholds
-   Creates multiple output formats for different components

### Stage 3: Enumerate Paths

-   Uses DFS algorithm to find all possible trading paths
-   Respects maximum path length constraints
-   Outputs path files for each unique token pair

### Stage 4: Run Allocation

-   Applies sophisticated allocation algorithm
-   Uses reranking for optimal path selection
-   Generates allocation plans for each test case

### Stage 5: Remove Cycles

-   Detects and removes cycles from allocation plans
-   Ensures plans are executable without conflicts
-   Logs all modifications for transparency

### Stage 6: Simulate Execution

-   Executes allocation plans against simulated blockchain
-   Captures actual execution results
-   Compares performance against benchmarks

## Configuration Options

-   `--block_number`: Target blockchain block for analysis
-   `--max_length`: Maximum path length (default: 3)
-   `--threshold`: Capacity filtering threshold (default: 1)
-   `--split_ticks`: Enable V3 tick splitting for better granularity

## Setup Requirements

### Dependencies

```bash
pip install -r requirements.txt
```

### Required Files

-   Token mappings in `data/node_name.txt`
-   Benchmark data in `data/benchmark/<BLOCK_NUMBER>_benchmark/`
-   Pool price data in `data/prices/block-<BLOCK_NUMBER>/`

### External Services

-   Local blockchain simulation API at `http://127.0.0.1:8080`
-   Compiled DFS executable (`dfs.exe` or `dfs`)

## Performance Considerations

-   Path enumeration can be computationally intensive for large graphs
-   Simulation requires active blockchain node connection
-   Memory usage scales with graph size and path complexity
-   Tick splitting significantly increases processing time but improves accuracy
