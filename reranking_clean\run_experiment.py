#!/usr/bin/env python3
"""
Master orchestrator for the data-driven path allocation workflow.

This script automates the entire experimental pipeline based on benchmark data for a specific block.

Workflow:
1.  Load Test Cases: Reads benchmark `extracted_data.csv` for a given block.
2.  Generate Graph: Creates the complete market graph for the block.
3.  Allocate Paths: For each benchmark case, runs the `path_allocation.py` algorithm.
4.  Simulate Paths: Executes the generated allocation plans against a node.
5.  Analyze Results: Compares the algorithm's performance against the benchmark and generates a CSV report.

Usage:
    python run_experiment.py --block_number 22737504
"""

import os
import sys
import json
import argparse
import subprocess
import time
import pandas as pd
from pathlib import Path

# --- Utility Functions ---

def run_command(cmd, description):
    """Executes a command, logs it, and handles errors."""
    print(f"\n{'='*80}")
    print(f"EXECUTING: {description}")
    print(f"COMMAND:   {cmd}")
    print(f"{'='*80}")
    try:
        # Using shell=True for simplicity. For production, a list of args is safer.
        result = subprocess.run(cmd, shell=True, check=True, text=True, capture_output=True)
        if result.stdout:
            print("STDOUT:\n" + result.stdout.strip())
        if result.stderr:
            print("STDERR:\n" + result.stderr.strip())
    except subprocess.CalledProcessError as e:
        print(f"\n❌ ERROR: {description} failed with return code {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        sys.exit(1)

def ensure_directories():
    """Create all necessary output directories and clean old files."""
    print("Ensuring output directories exist and are clean...")
    
    # Directories that should be cleaned before each run
    output_dirs = [
        "results/path_allocation_json",
        "results/simulation_json",
        "results/analysis"
    ]
    
    # Directories that should exist but not be cleaned
    data_dirs = [
        "data/graph_data"
    ]
    
    # Clean output directories
    for dir_path in output_dirs:
        dir_obj = Path(dir_path)
        if dir_obj.exists():
            # Remove all files in the directory
            file_count = 0
            for file in dir_obj.glob('*'):
                if file.is_file():
                    file.unlink()
                    file_count += 1
            if file_count > 0:
                print(f"  🧹 Cleaned {file_count} old files from {dir_path}")
        dir_obj.mkdir(parents=True, exist_ok=True)
        print(f"  ✓ {dir_path} (clean)")
    
    # Create data directories without cleaning
    for dir_path in data_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"  ✓ {dir_path}")

# --- Core Workflow Stages ---

def stage1_load_test_cases(block_number: int):
    """
    Build the Stage‑1 test‑case list by reading every *.json trade file found
    under data/benchmark/{block_number}_benchmark/.

    A valid file‑name must follow:
        {block}_{sell}-{buy}_{amount}.json
    and the JSON itself must contain a string field "fromAmount".
    """
    print(f"\n📋 STAGE 1: Loading Test Cases for Block {block_number}")

    benchmark_dir = Path(f"data/benchmark/{block_number}_benchmark")
    if not benchmark_dir.is_dir():
        print(f"❌ CRITICAL ERROR: {benchmark_dir} does not exist or is not a directory.")
        sys.exit(1)

    json_files = sorted(benchmark_dir.glob("*.json"))
    if not json_files:
        print(f"❌ CRITICAL ERROR: No *.json files found in {benchmark_dir}")
        sys.exit(1)

    test_cases = []
    for f in json_files:
        # File‑name format sanity‑check
        parts = f.stem.split("_")                 # drop the '.json'
        if len(parts) != 3 or "-" not in parts[1]:
            print(f"  ⚠️  Skipping malformed filename: {f.name}")
            continue

        pair  = parts[1].lower()                          # e.g. reth-wsteth
        # Fall back to name if JSON missing or invalid
        sell_amount_str = parts[2]

        # Load JSON once to extract authoritative amount
        try:
            with open(f, "r") as jf:
                data = json.load(jf)
            # prefer the on‑chain value if present & non‑empty
            sell_amount_str = str(data.get("fromAmount", sell_amount_str))
        except Exception as e:
            print(f"  ⚠️  {f.name}: could not parse JSON ({e}), "
                  f"using amount from filename.")

        test_cases.append({"pair": pair, "sell_amount": sell_amount_str})

    if not test_cases:
        print("❌ CRITICAL ERROR: No valid test cases could be constructed.")
        sys.exit(1)

    print(f"✓ Found {len(test_cases)} test cases to execute.")

    # ──────────────────────────────
    # STAGE 1.1: Validate token maps
    # ──────────────────────────────
    print("\n📋 STAGE 1.1: Validating Token Mappings")

    # Load node name mappings
    node_name_file = "data/node_name.txt"
    if not Path(node_name_file).exists():
        print(f"❌ CRITICAL ERROR: Node name file not found at {node_name_file}")
        sys.exit(1)
    
    # Read all available token mappings
    available_tokens = set()
    with open(node_name_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split()
                if len(parts) >= 2:
                    symbol = parts[0].upper()
                    available_tokens.add(symbol)
    print(f"✓ Found {len(available_tokens)} tokens in node_name.txt")

    valid_test_cases, missing_tokens = [], set()
    for case in test_cases:
        try:
            sell_sym, buy_sym = (s.upper() for s in case["pair"].split("-"))
        except ValueError:
            print(f"  ⚠️  Invalid pair format: {case['pair']}")
            continue

        if sell_sym not in available_tokens:
            missing_tokens.add(sell_sym)
        if buy_sym not in available_tokens:
            missing_tokens.add(buy_sym)

        if sell_sym in available_tokens and buy_sym in available_tokens:
            valid_test_cases.append(case)
        else:
            print(f"  ⚠️  Skipping {case['pair']}: missing token mapping")

    if missing_tokens:
        print(f"\n⚠️  Missing token mappings for: {', '.join(sorted(missing_tokens))}")

    print(f"✓ {len(valid_test_cases)} test cases remain after validation.")
    return valid_test_cases

def stage2_generate_graph(block_number, is_split_ticks=False, threshold=1):
    """Generates the market graph for the specified block."""
    print(f"\n📉 STAGE 2: Generating Market Graph for Block {block_number}")
    # This assumes `graph.py` has been refactored to accept block_number.
    cmd = f"python graph.py --block_number {block_number}"
    if is_split_ticks:
        cmd += " --split-ticks"
    
    cmd += f" --threshold {threshold}"
    run_command(cmd, "Market graph generation")
    
    graph_file = Path(f"data/graph_data/G_{block_number}.json")
    if not graph_file.is_file():
        print(f"❌ CRITICAL ERROR: Graph file not created at {graph_file}")
        sys.exit(1)
    
    print(f"✓ Graph file created: {graph_file}")

def stage3_enumerate_paths(block_number, test_cases, max_length=3):
    """Enumerates all possible paths for each unique token pair."""
    print(f"\n🔍 STAGE 3: Enumerating Paths for Token Pairs")
    
    # Extract unique pairs from test cases
    unique_pairs = set()
    for case in test_cases:
        unique_pairs.add(case['pair'])
    
    print(f"Found {len(unique_pairs)} unique token pairs to process")
    
    # Ensure results directory exists
    Path("results/path").mkdir(parents=True, exist_ok=True)
    
    # Load node mappings
    node_name_file = "data/node_name.txt"
    node_mapping_file = f"graphs/{block_number}_combined_node_map.txt"
    
    # Read node name mappings
    node_name = {}
    try:
        with open(node_name_file, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                if line:
                    parts = line.split()
                    symbol, address = parts[0], parts[1]
                    node_name[symbol.lower()] = address
    except FileNotFoundError:
        print(f"❌ CRITICAL ERROR: Node name file not found at {node_name_file}")
        sys.exit(1)
    
    # Read node index mappings
    node_index = {}
    try:
        with open(node_mapping_file, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                if line:
                    parts = line.split()
                    index, address = parts[0], parts[1]
                    node_index[address.lower()] = index
    except FileNotFoundError:
        print(f"❌ CRITICAL ERROR: Node mapping file not found at {node_mapping_file}")
        sys.exit(1)
    
    # Enumerate paths for each unique pair
    for i, pair in enumerate(unique_pairs, 1):
        print(f"\n--- Enumerating paths for pair {i}/{len(unique_pairs)}: {pair} ---")
        
        try:
            source_symbol, target_symbol = pair.split('-')
            source_symbol = source_symbol.lower()
            target_symbol = target_symbol.lower()
        except ValueError:
            print(f"⚠️  Warning: Invalid pair format: {pair}. Skipping.")
            continue
        
        # Get node indices
        source_address = node_name.get(source_symbol)
        target_address = node_name.get(target_symbol)
        
        if not source_address or not target_address:
            print(f"⚠️  Warning: Could not find addresses for {source_symbol} or {target_symbol}. Skipping.")
            continue
            
        source_id = node_index.get(source_address.lower())
        target_id = node_index.get(target_address.lower())
        
        if not source_id or not target_id:
            print(f"⚠️  Warning: Could not find node indices for {source_symbol} or {target_symbol}. Skipping.")
            continue
        
        # Define output file
        output_file = f"results/path/all_paths_{source_id}_{target_id}_{max_length}.txt"
        
        # Skip if path file already exists
        if Path(output_file).exists():
            print(f"✓ Path file already exists: {output_file}")
            continue
        
        # Run path enumeration
        graph_file = f"graphs/{block_number}_combined_token_graph.txt"

        exe_name = "dfs"               # same on both platforms
        cmd = f".{os.sep}{exe_name}"
        
        print(f"Running path enumeration from node {source_id} to {target_id} (max length: {max_length})")
        
        try:
            # Prepare input for the DFS program
            input_data = f"{graph_file}\n{source_id}\n{target_id}\n{max_length}\n"
            
            # Run the DFS executable
            result = subprocess.run(
                cmd, 
                shell=True, 
                input=input_data,
                text=True, 
                capture_output=True, 
                check=True,
                timeout=300  # 5 minute timeout
            )
            
            print(f"✓ Path enumeration completed for {pair}")
            if result.stdout:
                print(f"Found paths saved to: {output_file}")
                
        except subprocess.TimeoutExpired:
            print(f"⚠️  Warning: Path enumeration timed out for {pair}")
        except subprocess.CalledProcessError as e:
            print(f"⚠️  Warning: Path enumeration failed for {pair}: {e}")
            if e.stderr:
                print(f"Error: {e.stderr}")
    
    print(f"✓ Path enumeration completed for all unique pairs")

def stage4_run_allocation(block_number, test_cases, max_length=3, split_ticks=False):
    """Runs the allocation algorithm for each test case."""
    print(f"\n⚖️  STAGE 4: Running Allocation")
    
    total_cases = len(test_cases)
    for i, case in enumerate(test_cases):
        pair = case['pair']
        amount = case['sell_amount']
        
        print(f"\n--- Running Case {i+1}/{total_cases}: {pair} with amount {amount} ---")
        
        # This assumes `path_allocation.py` is refactored.
        cmd = (f"python path_allocation.py "
               f"--block_number {block_number} "
               f"--pairs {pair} "
               f"--amounts {amount} "
               f"--max_length {max_length}")
        
        if split_ticks:
            cmd += " --split-ticks"
        
        
        run_command(cmd, f"Allocation for {pair}")

    print(f"✓ Completed allocation for all {total_cases} cases.")


def stage5_romove_cycle():
    """Remove cycles from all generated allocation plans."""
    print(f"\n STAGE 5: Removing Cycles from Allocation Plans")
    directory_path = "results/path_allocation_json"
    cmd = f"python remove_cycles.py --directory {directory_path}"
    run_command(cmd, "Removing cycles from allocation plans")
    print(f"✓ Cycles removed from all allocation plans.")

def stage6_run_simulation(block_number):
    """Simulates all generated allocation plans."""
    print(f"\n🔬 STAGE 6: Simulating Allocation Plans")
    
    # Update the command to include the new required argument
    cmd = (f"python simulator.py "
           f"--block {block_number} "
           f'--folder "results/path_allocation_json" '
           f'--mode "batch" ')
           
    run_command(cmd, f"Simulating swaps for block {block_number}")
    print(f"✓ Simulation completed for block {block_number}.")

# --- Main Orchestrator ---

def main():
    parser = argparse.ArgumentParser(
        description="Run a data-driven experiment for the path algorithm against benchmark data.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        '--block_number',
        type=int,
        required=True,
        help="The block number to run the experiment against (e.g., 22737504)."
    )

    parser.add_argument(
        '--max_length',
        type=int,
        default=3,
        help="Maximum path length to consider (default: 3)."
    )

    parser.add_argument(
        '--threshold',
        type=float,
        default=1,
        help="Threshold for filtering ticks (default: 1)."
    )
    
    parser.add_argument('--split_ticks', action='store_true', 
                       help='Enable V3 tick splitting for large price ranges')

    args = parser.parse_args()
    block = args.block_number
    max_length = args.max_length
    split_ticks = args.split_ticks
    threshold = args.threshold

    print("="*80)
    print("🚀 STARTING AUTOMATED EXPERIMENT WORKFLOW")
    print(f"Target Block: {block}")
    print("="*80)
    
    start_time = time.time()
    
    try:
        ensure_directories()

        if split_ticks:
            cmd = (f"python process_v3_data.py "
            f"--block_number {block} ")
            run_command(cmd, f"Processing v3 data for {block}")
        
        test_cases = stage1_load_test_cases(block)
        stage2_generate_graph(block, split_ticks, threshold)
        stage3_enumerate_paths(block, test_cases, max_length)
        stage4_run_allocation(block, test_cases, max_length, split_ticks)
        stage5_romove_cycle()
        stage6_run_simulation(block)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n\n{'='*80}")
        print(f"✅ EXPERIMENT COMPLETED SUCCESSFULLY!")
        print(f"Total duration: {duration:.2f} seconds ({duration/60:.2f} minutes)")
        print(f"Final report available at: results/analysis/{block}_vs_benchmark.csv")
        print("="*80)
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Experiment interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ AN UNEXPECTED ERROR OCCURRED: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()